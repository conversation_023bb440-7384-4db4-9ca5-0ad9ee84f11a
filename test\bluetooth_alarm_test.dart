import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:flutter_application_3/main.dart';
import 'package:flutter_application_3/services/bluetooth_service_new.dart';
import 'package:flutter_application_3/screens/main_screen_new.dart';

void main() {
  testWidgets('Main screen test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (_) => BluetoothService(),
        child: const MaterialApp(
          home: MainScreen(),
        ),
      ),
    );

    await tester.pump();

    // Verify that basic elements are present
    expect(find.text('Bluetooth Device Scanner'), findsWidgets);
    expect(find.text('Scanned Devices (0)'), findsOneWidget);
    expect(find.text('Auto-scan every 10 seconds • Monitor saved devices'),
        findsOneWidget);
  });

  testWidgets('App test', (WidgetTester tester) async {
    // Test the main app
    await tester.pumpWidget(const BluetoothAlarmApp());
    await tester.pump();

    // Verify app loads without errors
    expect(find.byType(MaterialApp), findsOneWidget);
  });

  testWidgets('Bluetooth service initialization test',
      (WidgetTester tester) async {
    final bluetoothService = BluetoothService();

    // Test initial state
    expect(bluetoothService.devices, isEmpty);
    expect(bluetoothService.savedDevices, isEmpty);
    expect(bluetoothService.isScanning, false);
    expect(bluetoothService.isInitialized, false);
    expect(bluetoothService.statusMessage, 'Initializing...');

    // Test initialization (will handle platform not supported gracefully)
    await bluetoothService.initialize();
    expect(bluetoothService.isInitialized, true);
  });

  testWidgets('Device save and alarm toggle test', (WidgetTester tester) async {
    final bluetoothService = BluetoothService();
    const testAddress = '00:11:22:33:44:55';

    // Test initial state
    expect(bluetoothService.hasAlarm(testAddress), false);
    expect(bluetoothService.isSaved(testAddress), false);

    // Test dBm range setting
    bluetoothService.setDbmRange(-90, -40);
    expect(bluetoothService.minDbm, -90);
    expect(bluetoothService.maxDbm, -40);
  });
}
