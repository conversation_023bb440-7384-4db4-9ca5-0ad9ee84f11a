import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'services/bluetooth_service_new.dart';
import 'screens/main_screen_new.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const BluetoothAlarmApp());
}

class BluetoothAlarmApp extends StatelessWidget {
  const BluetoothAlarmApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => BluetoothService(),
      child: MaterialApp(
        title: 'Bluetooth Alarm',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: const MainScreen(),
      ),
    );
  }
}
