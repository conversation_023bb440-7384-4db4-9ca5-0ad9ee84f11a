import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/bluetooth_service_new.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Device Scanner'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => _showDbmRangeDialog(context),
            icon: const Icon(Icons.tune),
            tooltip: 'Set dBm Range',
          ),
        ],
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          return Column(
            children: [
              // Header with status and controls
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Icon(
                          Icons.bluetooth_searching,
                          size: 48,
                          color: Colors.blue,
                        ),
                        if (bluetoothService.isScanning)
                          const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          ElevatedButton.icon(
                            onPressed: () => bluetoothService.startScan(),
                            icon: const Icon(Icons.search),
                            label: const Text('Scan Now'),
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Bluetooth Device Scanner',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      bluetoothService.statusMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'dBm Range: ${bluetoothService.minDbm} to ${bluetoothService.maxDbm}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Auto-scan every 10 seconds • Monitor saved devices',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),

              // Saved devices section
              if (bluetoothService.savedDevices.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      Icon(Icons.bookmark, color: Colors.orange.shade600),
                      const SizedBox(width: 8),
                      Text(
                        'Saved Devices (${bluetoothService.savedDevices.length})',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                SizedBox(
                  height: 120,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: bluetoothService.savedDevices.length,
                    itemBuilder: (context, index) {
                      final device = bluetoothService.savedDevices[index];
                      return _buildSavedDeviceCard(device, bluetoothService);
                    },
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Scanned devices list
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        children: [
                          Icon(Icons.radar, color: Colors.blue.shade600),
                          const SizedBox(width: 8),
                          Text(
                            'Scanned Devices (${bluetoothService.devices.length})',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Expanded(
                      child: bluetoothService.devices.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.bluetooth_searching,
                                    size: 64,
                                    color: Colors.grey.shade400,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    bluetoothService.isInitialized
                                        ? 'No devices found in range\nTap "Scan Now" to search'
                                        : 'Initializing Bluetooth...',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: bluetoothService.devices.length,
                              itemBuilder: (context, index) {
                                final device = bluetoothService.devices[index];
                                return _buildDeviceCard(
                                    device, bluetoothService);
                              },
                            ),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSavedDeviceCard(
      BluetoothDeviceModel device, BluetoothService service) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: device.hasAlarm ? Colors.orange.shade50 : Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              device.hasAlarm ? Colors.orange.shade200 : Colors.blue.shade200,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                device.hasAlarm ? Icons.alarm_on : Icons.bookmark,
                color: device.hasAlarm ? Colors.orange : Colors.blue,
                size: 20,
              ),
              const Spacer(),
              GestureDetector(
                onTap: () => service.removeSavedDevice(device.address),
                child: Icon(
                  Icons.close,
                  color: Colors.grey.shade600,
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            device.name,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            device.address,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey.shade600,
            ),
          ),
          const Spacer(),
          GestureDetector(
            onTap: () => service.toggleAlarm(device.address),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: device.hasAlarm ? Colors.orange : Colors.grey.shade300,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                device.hasAlarm ? 'Alarm ON' : 'Alarm OFF',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: device.hasAlarm ? Colors.white : Colors.grey.shade700,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard(
      BluetoothDeviceModel device, BluetoothService service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ListTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: device.isSaved ? Colors.blue.shade100 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            _getDeviceIcon(device.name),
            color: device.isSaved ? Colors.blue : Colors.grey,
            size: 28,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              device.address,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getRssiColor(device.rssi),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${device.rssi} dBm',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (device.isSaved) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Saved',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: device.isSaved
            ? Icon(Icons.bookmark, color: Colors.blue.shade600)
            : ElevatedButton.icon(
                onPressed: () =>
                    service.saveDevice(device.address, device.name),
                icon: const Icon(Icons.bookmark_border, size: 16),
                label: const Text('Save', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(60, 32),
                ),
              ),
      ),
    );
  }

  Color _getRssiColor(int rssi) {
    if (rssi >= -50) return Colors.green;
    if (rssi >= -70) return Colors.orange;
    return Colors.red;
  }

  IconData _getDeviceIcon(String deviceName) {
    final name = deviceName.toLowerCase();
    if (name.contains('airpods') ||
        name.contains('buds') ||
        name.contains('headphone')) {
      return Icons.headset;
    } else if (name.contains('speaker')) {
      return Icons.speaker;
    } else if (name.contains('mouse')) {
      return Icons.mouse;
    } else if (name.contains('keyboard')) {
      return Icons.keyboard;
    } else if (name.contains('phone')) {
      return Icons.phone_android;
    } else if (name.contains('watch')) {
      return Icons.watch;
    } else {
      return Icons.bluetooth;
    }
  }

  void _showDbmRangeDialog(BuildContext context) {
    final service = context.read<BluetoothService>();
    int minDbm = service.minDbm;
    int maxDbm = service.maxDbm;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Set dBm Range'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Filter devices by signal strength'),
              const SizedBox(height: 16),
              Text('Minimum dBm: $minDbm'),
              Slider(
                value: minDbm.toDouble(),
                min: -100,
                max: -30,
                divisions: 70,
                onChanged: (value) {
                  setState(() {
                    minDbm = value.round();
                    if (minDbm >= maxDbm) {
                      maxDbm = minDbm + 10;
                    }
                  });
                },
              ),
              Text('Maximum dBm: $maxDbm'),
              Slider(
                value: maxDbm.toDouble(),
                min: -100,
                max: -30,
                divisions: 70,
                onChanged: (value) {
                  setState(() {
                    maxDbm = value.round();
                    if (maxDbm <= minDbm) {
                      minDbm = maxDbm - 10;
                    }
                  });
                },
              ),
              const SizedBox(height: 8),
              Text(
                'Range: $minDbm to $maxDbm dBm',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                service.setDbmRange(minDbm, maxDbm);
                Navigator.pop(context);
              },
              child: const Text('Apply'),
            ),
          ],
        ),
      ),
    );
  }
}
