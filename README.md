# Bluetooth Alarm App

A comprehensive Flutter application that monitors Bluetooth device connections and triggers customizable alarms when devices connect or disconnect. Features a beautiful neomorphism design and robust background monitoring capabilities.

## Features

### 🔗 Device Management

- **Bluetooth Scanning**: Discover and list nearby Bluetooth devices with real-time RSSI values
- **Device Pairing**: One-tap pairing/unpairing with visual feedback
- **Connection Status**: Real-time monitoring of device connection states
- **Device Information**: Display device names, MAC addresses, and connection history

### 🚨 Alarm System

- **Per-Device Configuration**: Set unique alarm settings for each paired device
- **Flexible Triggers**: Choose from connect, disconnect, or both events
- **Custom Sounds**: Support for default, notification, and custom alarm sounds
- **Volume Control**: Adjustable alarm volume with test functionality
- **Duration Settings**: Configurable alarm duration (1-60 seconds)
- **Vibration Support**: Optional vibration patterns
- **Visual Notifications**: In-app and system notifications

### 🎨 Neomorphism Design

- **Modern UI**: Beautiful neomorphic design elements throughout the app
- **Dark/Light Themes**: Automatic theme switching based on system preferences
- **Smooth Animations**: Staggered animations and smooth transitions
- **Custom Widgets**: Purpose-built neomorphic buttons, cards, and controls

### 🔄 Background Monitoring

- **Persistent Service**: Continues monitoring even when app is closed
- **Low Power**: Optimized for minimal battery usage
- **Auto-Start**: Automatically starts monitoring on device boot
- **Real-time Updates**: Instant alarm triggers on connection changes

## Technical Architecture

### Core Components

- **BluetoothService**: Handles device scanning, pairing, and connection monitoring
- **AlarmService**: Manages alarm configurations and triggers
- **BackgroundService**: Provides persistent monitoring capabilities
- **PermissionManager**: Handles all Bluetooth and system permissions

### Data Models

- **BluetoothDeviceModel**: Represents Bluetooth devices with connection states
- **AlarmConfigModel**: Stores alarm settings and preferences

### UI Components

- **NeomorphicWidgets**: Custom UI components with neomorphic styling
- **HomeScreen**: Main dashboard showing paired devices and alarm status
- **DeviceScanScreen**: Bluetooth scanning and device discovery interface
- **AlarmConfigScreen**: Detailed alarm configuration for each device

## Installation & Setup

### Prerequisites

- Flutter SDK (3.6.0 or higher)
- Android SDK (API level 21 or higher)
- Bluetooth-enabled device for testing

### Dependencies

```yaml
dependencies:
  flutter_bluetooth_serial: ^0.4.0
  permission_handler: ^11.3.1
  flutter_background_service: ^5.0.5
  audioplayers: ^6.0.0
  provider: ^6.1.2
  shared_preferences: ^2.2.3
  flutter_staggered_animations: ^1.1.1
```

### Installation Steps

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Ensure Android permissions are properly configured
4. Build and run the app: `flutter run`

## Permissions

The app requires the following permissions:

### Android

- `BLUETOOTH` - Basic Bluetooth functionality
- `BLUETOOTH_ADMIN` - Bluetooth device management
- `ACCESS_COARSE_LOCATION` - Required for Bluetooth scanning
- `ACCESS_FINE_LOCATION` - Enhanced location accuracy
- `BLUETOOTH_SCAN` - Android 12+ scanning permission
- `BLUETOOTH_CONNECT` - Android 12+ connection permission
- `FOREGROUND_SERVICE` - Background monitoring
- `WAKE_LOCK` - Keep device awake for monitoring
- `RECEIVE_BOOT_COMPLETED` - Auto-start on device boot

## Usage Guide

### Initial Setup

1. **Grant Permissions**: Allow Bluetooth and location permissions when prompted
2. **Enable Bluetooth**: Ensure Bluetooth is enabled on your device
3. **Scan for Devices**: Use the floating action button to discover nearby devices

### Device Management

1. **Pair Devices**: Tap "Pair" on discovered devices to establish connection
2. **Monitor Status**: View real-time connection status on the home screen
3. **Configure Alarms**: Tap on paired devices to set up alarm preferences

### Alarm Configuration

1. **Choose Trigger**: Select when alarms should activate (connect/disconnect/both)
2. **Sound Settings**: Pick alarm sound type and adjust volume
3. **Duration**: Set how long alarms should play (1-60 seconds)
4. **Additional Options**: Enable vibration and notifications as needed
5. **Test Alarm**: Use the test button to preview your settings

### Background Monitoring

1. **Enable Service**: Tap the notification icon in the header to start monitoring
2. **Auto-Start**: The service will automatically start on device reboot
3. **Status Indicator**: Monitor service status through the notification icon

## Customization

### Adding Custom Sounds

1. Place audio files in `assets/sounds/` directory
2. Update `pubspec.yaml` to include new sound files
3. Modify `AlarmConfigModel` to support additional sound types

### Theming

- Light and dark themes are automatically applied based on system settings
- Customize colors in `utils/constants.dart`
- Modify neomorphic styling in `widgets/neomorphic_widgets.dart`

## Testing

Run the included tests:

```bash
flutter test
```

The test suite includes:

- Widget tests for main UI components
- Navigation flow testing
- Basic functionality verification

## Troubleshooting

### Common Issues

1. **Bluetooth Not Working**: Ensure all permissions are granted and Bluetooth is enabled
2. **Background Service Not Starting**: Check that foreground service permissions are granted
3. **Alarms Not Triggering**: Verify alarm configurations and background service status
4. **Device Not Found**: Ensure target device is discoverable and within range

### Debug Mode

- Enable debug logging by setting `debugPrint` statements throughout the codebase
- Use Flutter Inspector to examine widget tree and state management
- Monitor background service logs through Android Studio or ADB

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes with proper testing
4. Submit a pull request with detailed description

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Flutter team for the excellent framework
- Plugin developers for Bluetooth and background service support
- Design inspiration from modern neomorphism trends
