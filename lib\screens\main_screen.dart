import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/bluetooth_service_new.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize Bluetooth service
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<BluetoothService>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Alarm'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        centerTitle: true,
      ),
      body: Consumer<BluetoothService>(
        builder: (context, bluetoothService, child) {
          final devices = bluetoothService.devices;

          return Column(
            children: [
              // Header info with status
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Icon(
                          Icons.bluetooth,
                          size: 48,
                          color: Colors.blue,
                        ),
                        if (bluetoothService.isScanning)
                          const SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          IconButton(
                            onPressed: () => bluetoothService.startScan(),
                            icon: const Icon(Icons.refresh),
                            tooltip: 'Refresh devices',
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Bluetooth Devices',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      bluetoothService.statusMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Scan → Save → Pair → Set Alarm',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),

              // Device list
              Expanded(
                child: devices.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.bluetooth_searching,
                              size: 64,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 16),
                            Text(
                              bluetoothService.isInitialized
                                  ? 'No devices found\nTap refresh to scan again'
                                  : 'Initializing Bluetooth...',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey.shade600,
                              ),
                            ),
                            if (bluetoothService.isInitialized) ...[
                              const SizedBox(height: 16),
                              ElevatedButton.icon(
                                onPressed: () => bluetoothService.startScan(),
                                icon: const Icon(Icons.search),
                                label: const Text('Scan for Devices'),
                              ),
                              const SizedBox(height: 8),
                              TextButton(
                                onPressed: () => bluetoothService.initialize(),
                                child: const Text('Retry Initialization'),
                              ),
                            ],
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: devices.length,
                        itemBuilder: (context, index) {
                          final device = devices[index];
                          return _buildDeviceCard(device, bluetoothService);
                        },
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDeviceCard(
      BluetoothDeviceModel device, BluetoothService service) {
    final hasAlarm = service.hasAlarm(device.address);
    final isSaved = service.isSaved(device.address);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      child: ExpansionTile(
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: device.isConnected
                ? Colors.green.shade100
                : device.isSaved
                    ? Colors.blue.shade100
                    : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(25),
          ),
          child: Icon(
            _getDeviceIcon(device.name),
            color: device.isConnected
                ? Colors.green
                : device.isSaved
                    ? Colors.blue
                    : Colors.grey,
            size: 28,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              device.address,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: device.isConnected ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    device.isConnected ? 'Connected' : 'Disconnected',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (device.rssi != 0) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${device.rssi} dBm',
                      style: TextStyle(
                        color: Colors.grey.shade700,
                        fontSize: 9,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
                if (isSaved) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Saved',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                if (hasAlarm) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      'Alarm Set',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () async {
                          await service.toggleSaveDevice(device.address);
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  isSaved
                                      ? 'Device removed from monitoring'
                                      : 'Device saved for monitoring',
                                ),
                                duration: const Duration(seconds: 2),
                              ),
                            );
                          }
                        },
                        icon: Icon(
                            isSaved ? Icons.bookmark : Icons.bookmark_border),
                        label: Text(isSaved ? 'Unsave' : 'Save'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isSaved ? Colors.red : Colors.blue,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: device.isConnected
                            ? null
                            : () async {
                                final success =
                                    await service.pairDevice(device.address);
                                if (mounted) {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        success
                                            ? 'Device paired successfully'
                                            : 'Failed to pair device',
                                      ),
                                      backgroundColor:
                                          success ? Colors.green : Colors.red,
                                      duration: const Duration(seconds: 2),
                                    ),
                                  );
                                }
                              },
                        icon: const Icon(Icons.bluetooth_connected),
                        label: Text(device.isConnected ? 'Connected' : 'Pair'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              device.isConnected ? Colors.green : Colors.orange,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Text('Disconnect Alarm: '),
                    Switch(
                      value: hasAlarm,
                      onChanged: isSaved
                          ? (_) {
                              service.toggleAlarm(device.address);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    hasAlarm
                                        ? 'Alarm disabled for ${device.name}'
                                        : 'Alarm enabled for ${device.name}',
                                  ),
                                  duration: const Duration(seconds: 2),
                                  backgroundColor:
                                      hasAlarm ? Colors.red : Colors.green,
                                ),
                              );
                            }
                          : null,
                      activeColor: Colors.orange,
                    ),
                    if (!isSaved)
                      Text(
                        'Save device first',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  IconData _getDeviceIcon(String deviceName) {
    final name = deviceName.toLowerCase();

    if (name.contains('airpods') ||
        name.contains('buds') ||
        name.contains('headphone')) {
      return Icons.headset;
    } else if (name.contains('speaker')) {
      return Icons.speaker;
    } else if (name.contains('mouse')) {
      return Icons.mouse;
    } else if (name.contains('keyboard')) {
      return Icons.keyboard;
    } else if (name.contains('phone')) {
      return Icons.phone_android;
    } else if (name.contains('watch')) {
      return Icons.watch;
    } else {
      return Icons.bluetooth;
    }
  }
}
