# Bluetooth Alarm App - Installation Guide

## 📱 APK Location
Your APK file is located at: `build\app\outputs\flutter-apk\app-release.apk`

**File Size:** 19.8MB

## 🚀 Installation Instructions

### Method 1: Direct Installation
1. Copy the `app-release.apk` file to your Android device
2. On your Android device, go to **Settings > Security**
3. Enable **"Install from Unknown Sources"** or **"Allow from this source"**
4. Navigate to the APK file using a file manager
5. Tap on the APK file and follow the installation prompts

### Method 2: ADB Installation (if you have ADB installed)
```bash
adb install build\app\outputs\flutter-apk\app-release.apk
```

## 📋 App Features

### ✅ Fixed Issues
- **Alarm Detection**: Fixed the main issue where alarms weren't triggering when saved devices disappeared during rescans
- **Time-based Logic**: Added proper timing logic to avoid false alarms (20-second detection window)
- **Alarm Throttling**: Prevents alarm spam by limiting alarms to once per minute per device
- **Enhanced Audio**: Multiple system alert sounds for better notification
- **Robust Scanning**: 10-second scan intervals with proper device monitoring

### 🔧 Core Functionality
1. **Device Scanning**: Automatically scans for Bluetooth devices every 10 seconds
2. **Device Saving**: Save devices you want to monitor
3. **Alarm Setting**: Enable disconnect alarms for saved devices
4. **dBm Filtering**: Configurable signal strength range (-80 to -30 dBm by default)
5. **Real-time Monitoring**: Continuous monitoring of saved devices with alarms enabled

### 🎯 How It Works
1. **Scan**: App automatically scans for Bluetooth devices
2. **Save**: Tap on a device and save it for monitoring
3. **Alarm**: Enable the alarm toggle for the saved device
4. **Monitor**: App will trigger alarm when the device is not detected for more than 20 seconds

### 🔊 Alarm System
- **Multiple Alert Sounds**: Plays system alert sounds multiple times
- **Visual Feedback**: Status message shows alarm state
- **Smart Timing**: Prevents false alarms and spam notifications
- **Background Monitoring**: Continues monitoring even when app is in background

## 📱 Permissions Required
The app will request the following permissions:
- **Bluetooth**: For scanning and connecting to devices
- **Location**: Required for Bluetooth scanning on Android
- **Audio**: For playing alarm sounds

## 🔧 Troubleshooting

### If Bluetooth scanning doesn't work:
1. Ensure Bluetooth is enabled on your device
2. Grant all requested permissions
3. Make sure target devices are discoverable
4. Check if your device supports Bluetooth Low Energy (BLE)

### If alarms don't trigger:
1. Ensure the device is saved and alarm is enabled
2. Check that the target device is actually going out of range
3. Wait at least 20 seconds after device disappears
4. Verify app has audio permissions

### If no devices are found:
1. Adjust dBm range in settings (tap tune icon)
2. Make sure target devices are in discoverable mode
3. Move closer to the target devices
4. Try refreshing the scan manually

## 📊 Technical Details
- **Scan Interval**: 10 seconds
- **Detection Timeout**: 20 seconds
- **Alarm Cooldown**: 1 minute per device
- **dBm Range**: -80 to -30 (configurable)
- **Platform**: Android 5.0+ (API 21+)

## 🎨 UI Features
- **Neomorphism Design**: Modern, clean interface
- **Real-time Updates**: Live device list updates
- **Status Indicators**: Clear visual feedback
- **Easy Controls**: Simple save/alarm toggles
- **Device Information**: Shows device name, address, and signal strength

## 🔄 App Workflow
1. **Launch** → App initializes Bluetooth
2. **Scan** → Automatically finds nearby devices
3. **Save** → Select devices to monitor
4. **Alarm** → Enable disconnect alerts
5. **Monitor** → App watches for device presence
6. **Alert** → Alarm triggers when device disappears

---

**Note**: This app is designed for monitoring Bluetooth device presence and will work best with devices that regularly advertise their presence (like headphones, speakers, fitness trackers, etc.).
