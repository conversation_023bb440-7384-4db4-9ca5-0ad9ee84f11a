import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:audioplayers/audioplayers.dart';

// Enhanced device model for real Bluetooth functionality
class BluetoothDeviceModel {
  final String name;
  final String address;
  final bool isConnected;
  final bool isPaired;
  final bool hasAlarm;
  final bool isSaved;
  final BluetoothDevice? device; // Real flutter_blue_plus device
  final int rssi;
  final DateTime lastSeen;

  BluetoothDeviceModel({
    required this.name,
    required this.address,
    required this.isConnected,
    this.isPaired = false,
    this.hasAlarm = false,
    this.isSaved = false,
    this.device,
    this.rssi = 0,
    DateTime? lastSeen,
  }) : lastSeen = lastSeen ?? DateTime.now();

  BluetoothDeviceModel copyWith({
    String? name,
    String? address,
    bool? isConnected,
    bool? isPaired,
    bool? hasAlarm,
    bool? isSaved,
    BluetoothDevice? device,
    int? rssi,
    DateTime? lastSeen,
  }) {
    return BluetoothDeviceModel(
      name: name ?? this.name,
      address: address ?? this.address,
      isConnected: isConnected ?? this.isConnected,
      isPaired: isPaired ?? this.isPaired,
      hasAlarm: hasAlarm ?? this.hasAlarm,
      isSaved: isSaved ?? this.isSaved,
      device: device ?? this.device,
      rssi: rssi ?? this.rssi,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  // Create from flutter_blue_plus device
  factory BluetoothDeviceModel.fromBluetoothDevice(
    BluetoothDevice device,
    bool isConnected, {
    bool isPaired = false,
    bool hasAlarm = false,
    bool isSaved = false,
    int rssi = 0,
  }) {
    return BluetoothDeviceModel(
      name: device.platformName.isNotEmpty
          ? device.platformName
          : 'Unknown Device',
      address: device.remoteId.str,
      isConnected: isConnected,
      isPaired: isPaired,
      hasAlarm: hasAlarm,
      isSaved: isSaved,
      device: device,
      rssi: rssi,
    );
  }
}

class BluetoothService extends ChangeNotifier {
  final List<BluetoothDeviceModel> _scannedDevices = [];
  final List<BluetoothDeviceModel> _savedDevices = [];
  final Map<String, bool> _alarmSettings = {};
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanSubscription;
  Timer? _scanTimer;
  Timer? _deviceMonitorTimer;
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isScanning = false;
  bool _isInitialized = false;
  String _statusMessage = 'Initializing...';
  int _minDbm = -80; // Default minimum dBm
  int _maxDbm = -30; // Default maximum dBm

  List<BluetoothDeviceModel> get devices =>
      [..._scannedDevices, ..._savedDevices];
  List<BluetoothDeviceModel> get scannedDevices => _scannedDevices;
  List<BluetoothDeviceModel> get savedDevices => _savedDevices;
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  String get statusMessage => _statusMessage;
  int get minDbm => _minDbm;
  int get maxDbm => _maxDbm;

  bool hasAlarm(String address) => _alarmSettings[address] ?? false;
  bool isSaved(String address) =>
      _savedDevices.any((d) => d.address == address);

  // Set dBm range for filtering
  void setDbmRange(int minDbm, int maxDbm) {
    _minDbm = minDbm;
    _maxDbm = maxDbm;
    notifyListeners();
  }

  // Initialize real Bluetooth functionality
  Future<void> initialize() async {
    try {
      _statusMessage = 'Checking Bluetooth support...';
      notifyListeners();

      // Check if Bluetooth is supported
      try {
        if (await FlutterBluePlus.isSupported == false) {
          _statusMessage = 'Bluetooth not supported on this device';
          _isInitialized = true;
          notifyListeners();
          return;
        }
      } catch (e) {
        // Handle platform not supported (like in tests)
        _statusMessage = 'Bluetooth not available in current environment';
        _isInitialized = true;
        notifyListeners();
        return;
      }

      // Request permissions first
      _statusMessage = 'Requesting Bluetooth permissions...';
      notifyListeners();

      final permissionsGranted = await _requestPermissions();
      if (!permissionsGranted) {
        _statusMessage =
            'Bluetooth permissions denied. Please enable in settings.';
        _isInitialized = true;
        notifyListeners();
        return;
      }

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        _handleAdapterStateChange(state);
      });

      // Get initial adapter state and wait for it to be ready
      _statusMessage = 'Checking Bluetooth adapter...';
      notifyListeners();

      final adapterState = await FlutterBluePlus.adapterState.first.timeout(
        const Duration(seconds: 5),
        onTimeout: () => BluetoothAdapterState.unknown,
      );
      _handleAdapterStateChange(adapterState);

      // Load saved settings
      await _loadAlarmSettings();
      await _loadSavedDevices();

      _isInitialized = true;
      notifyListeners();

      // If Bluetooth is on, start initial scan
      if (adapterState == BluetoothAdapterState.on) {
        await startScan();
        _startDeviceMonitoring();
      }
    } catch (e) {
      _statusMessage = 'Error initializing Bluetooth: $e';
      _isInitialized = true;
      debugPrint('Bluetooth initialization error: $e');
      notifyListeners();
    }
  }

  // Request necessary permissions for Bluetooth
  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      try {
        // Check Android version and request appropriate permissions
        final permissions = <Permission>[];

        // Always needed permissions
        permissions.addAll([
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.location,
        ]);

        // Check current permission status first
        final currentStatuses = await permissions.request();

        // Check if essential permissions are granted
        bool hasBluetoothScan = currentStatuses[Permission.bluetoothScan] ==
            PermissionStatus.granted;
        bool hasBluetoothConnect =
            currentStatuses[Permission.bluetoothConnect] ==
                PermissionStatus.granted;
        bool hasLocation =
            currentStatuses[Permission.location] == PermissionStatus.granted;

        debugPrint('Permission status:');
        debugPrint('  Bluetooth Scan: $hasBluetoothScan');
        debugPrint('  Bluetooth Connect: $hasBluetoothConnect');
        debugPrint('  Location: $hasLocation');

        // For scanning to work properly, we need at least scan and location
        if (!hasBluetoothScan || !hasLocation) {
          debugPrint('Missing essential permissions for Bluetooth scanning');
          return false;
        }

        // For connecting to devices, we need connect permission
        if (!hasBluetoothConnect) {
          debugPrint(
              'Missing Bluetooth connect permission - some features may not work');
          // Still return true as we can at least scan
        }

        return true;
      } catch (e) {
        debugPrint('Error requesting permissions: $e');
        return false;
      }
    }
    return true; // iOS doesn't need explicit permissions for Bluetooth
  }

  // Handle Bluetooth adapter state changes
  void _handleAdapterStateChange(BluetoothAdapterState state) {
    switch (state) {
      case BluetoothAdapterState.on:
        _statusMessage = 'Bluetooth is on';
        // When Bluetooth turns on, load paired devices and start scanning
        _loadPairedDevices();
        break;
      case BluetoothAdapterState.off:
        _statusMessage = 'Bluetooth is off - Please enable Bluetooth';
        _devices.clear();
        break;
      case BluetoothAdapterState.turningOn:
        _statusMessage = 'Bluetooth is turning on...';
        break;
      case BluetoothAdapterState.turningOff:
        _statusMessage = 'Bluetooth is turning off...';
        break;
      default:
        _statusMessage = 'Bluetooth state unknown';
    }
    notifyListeners();
  }

  // Load paired/bonded devices
  Future<void> _loadPairedDevices() async {
    try {
      _statusMessage = 'Loading paired devices...';
      notifyListeners();

      // Get system bonded devices (Android only)
      if (Platform.isAndroid) {
        try {
          final bondedDevices = await FlutterBluePlus.bondedDevices;
          for (final device in bondedDevices) {
            _addOrUpdateDevice(device, false); // Assume not connected initially
          }
          debugPrint('Loaded ${bondedDevices.length} bonded devices');
        } catch (e) {
          debugPrint('Error getting bonded devices: $e');
        }
      }

      // Get currently connected devices
      try {
        final connectedDevices = FlutterBluePlus.connectedDevices;
        for (final device in connectedDevices) {
          _addOrUpdateDevice(device, true);
        }
        debugPrint('Found ${connectedDevices.length} connected devices');
      } catch (e) {
        debugPrint('Error getting connected devices: $e');
      }

      if (_devices.isNotEmpty) {
        _statusMessage = 'Found ${_devices.length} device(s)';
      } else {
        _statusMessage = 'No paired devices found';
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
      _statusMessage = 'Error loading devices: $e';
      notifyListeners();
    }
  }

  // Load saved alarm settings from SharedPreferences
  Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('alarm_'));
      for (final key in keys) {
        final address = key.substring(6); // Remove 'alarm_' prefix
        _alarmSettings[address] = prefs.getBool(key) ?? false;
      }
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
    }
  }

  // Load saved devices from SharedPreferences
  Future<void> _loadSavedDevices() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('saved_'));
      for (final key in keys) {
        final address = key.substring(6); // Remove 'saved_' prefix
        _savedDevices[address] = prefs.getBool(key) ?? false;
      }
      debugPrint('Loaded ${_savedDevices.length} saved devices');
    } catch (e) {
      debugPrint('Error loading saved devices: $e');
    }
  }

  // Save alarm setting to SharedPreferences
  Future<void> _saveAlarmSetting(String address, bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('alarm_$address', enabled);
    } catch (e) {
      debugPrint('Error saving alarm setting: $e');
    }
  }

  // Start scanning for Bluetooth devices
  Future<void> startScan() async {
    if (_isScanning) return;

    try {
      // Check if Bluetooth is available and on
      final adapterState = await FlutterBluePlus.adapterState.first.timeout(
        const Duration(seconds: 2),
        onTimeout: () => BluetoothAdapterState.unknown,
      );

      if (adapterState != BluetoothAdapterState.on) {
        _statusMessage = 'Bluetooth must be turned on to scan for devices';
        notifyListeners();
        return;
      }

      _isScanning = true;
      _statusMessage = 'Scanning for devices...';
      notifyListeners();

      // Clear devices list to start fresh scan
      _devices.clear();

      // Start scanning for devices
      _scanSubscription?.cancel();
      _scanSubscription = FlutterBluePlus.scanResults.listen(
        (results) {
          for (final result in results) {
            // Add all discoverable devices (with or without names)
            _addOrUpdateDevice(result.device, false, rssi: result.rssi);
          }
        },
        onError: (error) {
          debugPrint('Scan results error: $error');
        },
      );

      // Start scan with more permissive settings
      await FlutterBluePlus.startScan(
        timeout: const Duration(seconds: 15),
        androidUsesFineLocation: true,
      );

      // Wait for scan to complete
      await Future.delayed(const Duration(seconds: 15));

      _isScanning = false;

      // Update status message
      final savedCount = _devices.where((d) => d.isSaved).length;
      final totalCount = _devices.length;

      if (totalCount == 0) {
        _statusMessage =
            'No devices found. Make sure devices are discoverable.';
      } else {
        _statusMessage = 'Found $totalCount device(s) ($savedCount saved)';
      }

      notifyListeners();
    } catch (e) {
      _isScanning = false;
      _statusMessage = 'Scan error: ${e.toString()}';
      debugPrint('Bluetooth scan error: $e');
      notifyListeners();
    }
  }

  // Add or update device in the list
  void _addOrUpdateDevice(BluetoothDevice device, bool isConnected,
      {int rssi = 0}) {
    final address = device.remoteId.str;
    final existingIndex = _devices.indexWhere((d) => d.address == address);

    // Check if device is bonded/paired
    bool isPaired = false;
    try {
      // This will be updated when we check bonded devices
      isPaired = false; // We'll determine this separately
    } catch (e) {
      isPaired = false;
    }

    final deviceModel = BluetoothDeviceModel.fromBluetoothDevice(
      device,
      isConnected,
      isPaired: isPaired,
      hasAlarm: _alarmSettings[address] ?? false,
      isSaved: _savedDevices[address] ?? false,
      rssi: rssi,
    );

    if (existingIndex >= 0) {
      _devices[existingIndex] = deviceModel;
    } else {
      _devices.add(deviceModel);
    }

    // Monitor connection state for this device if it's saved
    if (_savedDevices[address] == true) {
      _monitorDeviceConnection(device);
    }

    notifyListeners();
  }

  // Monitor individual device connection state
  void _monitorDeviceConnection(BluetoothDevice device) {
    final address = device.remoteId.str;

    // Cancel existing subscription if any
    _connectionSubscriptions[address]?.cancel();

    // Listen to connection state changes
    _connectionSubscriptions[address] = device.connectionState.listen((state) {
      final isConnected = state == BluetoothConnectionState.connected;
      _updateDeviceConnectionState(address, isConnected);
    });
  }

  // Update device connection state
  void _updateDeviceConnectionState(String address, bool isConnected) {
    final deviceIndex = _devices.indexWhere((d) => d.address == address);
    if (deviceIndex >= 0) {
      final oldDevice = _devices[deviceIndex];

      // Check if this is a disconnection and alarm is set
      if (oldDevice.isConnected && !isConnected && hasAlarm(address)) {
        _triggerDisconnectAlarm(oldDevice);
      }

      _devices[deviceIndex] = oldDevice.copyWith(isConnected: isConnected);
      notifyListeners();
    }
  }

  // Save/unsave a device
  Future<void> toggleSaveDevice(String address) async {
    _savedDevices[address] = !(_savedDevices[address] ?? false);

    // Update device list to reflect saved status
    _devices = _devices.map((device) {
      if (device.address == address) {
        return device.copyWith(isSaved: _savedDevices[address]);
      }
      return device;
    }).toList();

    // Save setting
    await _saveSavedDevice(address, _savedDevices[address] ?? false);

    notifyListeners();

    // Show feedback
    final device = _devices.firstWhere((d) => d.address == address);
    if (_savedDevices[address] == true) {
      debugPrint('Device ${device.name} saved for monitoring');
    } else {
      debugPrint('Device ${device.name} removed from monitoring');
    }
  }

  void toggleAlarm(String address) {
    _alarmSettings[address] = !(_alarmSettings[address] ?? false);

    // Update device list to reflect alarm status
    _devices = _devices.map((device) {
      if (device.address == address) {
        return device.copyWith(hasAlarm: _alarmSettings[address]);
      }
      return device;
    }).toList();

    // Save setting
    _saveAlarmSetting(address, _alarmSettings[address] ?? false);

    notifyListeners();

    // Show feedback
    if (_alarmSettings[address] == true) {
      _showAlarmSetMessage(address);
    }
  }

  // Save device setting to SharedPreferences
  Future<void> _saveSavedDevice(String address, bool saved) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('saved_$address', saved);
    } catch (e) {
      debugPrint('Error saving device setting: $e');
    }
  }

  void _showAlarmSetMessage(String address) {
    try {
      final device = _devices.firstWhere((d) => d.address == address);
      debugPrint(
          'Alarm set for ${device.name} - will trigger when disconnected');
    } catch (e) {
      debugPrint(
          'Alarm set for device $address - will trigger when disconnected');
    }
  }

  void _triggerDisconnectAlarm(BluetoothDeviceModel device) {
    debugPrint('🚨 ALARM: ${device.name} disconnected!');

    // Play system sound
    SystemSound.play(SystemSoundType.alert);

    // Play custom alarm sound
    _playAlarmSound();

    // In a real app, you could also:
    // - Show notification
    // - Vibrate device
    // - Send to background service
  }

  Future<void> _playAlarmSound() async {
    try {
      // Play system sound as fallback since we don't have a real audio file
      SystemSound.play(SystemSoundType.alert);

      // In a real implementation, you would use:
      // await _audioPlayer.play(AssetSource('sounds/alarm.mp3'));
    } catch (e) {
      debugPrint('Error playing alarm sound: $e');
    }
  }

  // Pair with a device
  Future<bool> pairDevice(String address) async {
    try {
      final device = _devices.firstWhere((d) => d.address == address).device;
      if (device == null) return false;

      _statusMessage = 'Pairing with device...';
      notifyListeners();

      // Connect to the device first
      await device.connect(timeout: const Duration(seconds: 15));

      // Update device status
      _updateDeviceConnectionState(address, true);

      _statusMessage = 'Device paired successfully';
      notifyListeners();

      return true;
    } catch (e) {
      _statusMessage = 'Failed to pair device: $e';
      debugPrint('Pairing error: $e');
      notifyListeners();
      return false;
    }
  }

  // Start monitoring saved devices for disconnection
  void _startDeviceMonitoring() {
    _deviceMonitorTimer?.cancel();
    _deviceMonitorTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkSavedDevices();
    });
    debugPrint('Started device monitoring');
  }

  // Check if saved devices are still detectable
  Future<void> _checkSavedDevices() async {
    final savedDevices =
        _devices.where((d) => d.isSaved && d.hasAlarm).toList();

    for (final savedDevice in savedDevices) {
      if (savedDevice.device != null) {
        try {
          // Check connection state
          final connectionState =
              await savedDevice.device!.connectionState.first;
          final isConnected =
              connectionState == BluetoothConnectionState.connected;

          // If device was connected but now disconnected, trigger alarm
          if (savedDevice.isConnected && !isConnected) {
            _triggerDisconnectAlarm(savedDevice);
          }

          // Update device state
          _updateDeviceConnectionState(savedDevice.address, isConnected);
        } catch (e) {
          // Device not detectable - trigger alarm if it was previously connected
          if (savedDevice.isConnected) {
            _triggerDisconnectAlarm(savedDevice);
            _updateDeviceConnectionState(savedDevice.address, false);
          }
        }
      }
    }
  }

  // Stop scanning
  Future<void> stopScan() async {
    if (_isScanning) {
      await FlutterBluePlus.stopScan();
      _isScanning = false;
      _statusMessage = 'Scan stopped';
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _scanSubscription?.cancel();
    _adapterStateSubscription?.cancel();
    _deviceMonitorTimer?.cancel();
    for (final subscription in _connectionSubscriptions.values) {
      subscription.cancel();
    }
    _audioPlayer.dispose();
    super.dispose();
  }
}
